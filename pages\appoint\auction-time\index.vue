<!-- 约档组件 -->
<template>
	<view
		:style="{paddingBottom:`${newData.paddingBottom}px`,backgroundColor:`${newData.background}`}">
		<view v-if="calendarVisible" class="content">
			<view class="container" style="padding-bottom: 120rpx">
				<!-- 店铺列表 -->
				<view class="shop_select" v-if="shopArray">
					<view class="flex align-center justify-center fontw">
						<view class="flex-sub">
							店铺选择:
						</view>
						<view class="flex-twice" v-if="shopArray[shopIndex]">
							<view class="flex justify-center align-center">
								<picker @change="bindPickerChange" :value="shopIndex" :range-key="'name'"
									:range="shopArray">
									<view class="shop-picker-btn">
										{{shopArray[shopIndex].name}}
										<text class="picker-arrow">▼</text>
									</view>
								</picker>
							</view>
						</view>
					</view>
				</view>
				<!-- 日期列表 -->
				<view class="date_select flex align-center justify-center bg-white fontw padding-top-sm">
					<view class="date_select_range">
						<view class="cuIcon-back" v-if="checkIcon('back')" @click="changDate('back')"
							hover-class="hover-class" hover-stay-time="150">
						</view>
						<view class="cuIcon-center">
							{{dateArr[0].date +"~"+dateArr[dateArr.length-1].date}}
						</view>
						<view class="cuIcon-right" v-if="checkIcon('next')" @click="changDate('next')"
							hover-class="hover-class" hover-stay-time="150">
						</view>
					</view>
					<view v-if="checkDateSelect()" @click="openCalendar" class="more-time-btn">
						更多时间选择
					</view>
				</view>
				<scroll-view class="scroll-view_H b-t b-b" scroll-x>
					<block v-for="(item,index) in dateArr" :key="index">
						<view class="flex-box" @click="selectDateEvent(index,item)"
							:class="{ borderb: index==dateActive}">
							<view class="date-box" :style="{color:index==dateActive?newData.selectedTabColor:'#333'}">
								<text class="fontw">{{item.week}}</text>
								<text>{{item.date}}</text>
							</view>
						</view>
					</block>
				</scroll-view>

				<!-- 时间选项 -->
				<view class="time-box" v-if="!newData.isSection">
					<block v-for="(item,_index) in timeArr" :key="_index">
						<view class="item">
							<view class="item-box" :class="{'disable':item.disable,
							'active':newData.isMultiple?item.isActive:_index==timeActive}" :style="{color:newData.isMultiple?item.isActive? newData.selectedItemColor:'#333'
							 :_index==timeActive?newData.selectedItemColor:'#333'}" @click="selectTimeEvent(_index,item)"
							 hover-class="item-hover" hover-stay-time="150">
								<text>{{item.time}}</text>
								<text class="all">{{item.disable?newData.disableText:(item.availableCount > 0 ? /* '可约'+item.availableCount+'位' */ '可以约': newData.unDisableText)}}</text>
							</view>
						</view>
					</block>
				</view>
				<!-- 预约时间段 -->
				<view class="time-box" v-else>
					<block v-for="(item,_index) in timeArr" :key="_index">
						<view class="item">
							<view class="item-box" :class="{'disable':item.disable || item.isInclude,
							'active':item.time == timeQuanBegin || item.time == timeQuanEnd }"
								:style="{color:item.time == timeQuanBegin || item.time == timeQuanEnd? newData.selectedItemColor:'#333'}"
								@click="handleSelectQuantum(_index,item)" hover-class="item-hover" hover-stay-time="150">
								<text>{{item.time}}</text>
								<text class="all">{{item.disable?newData.disableText:(item.availableCount > 0 ? /* '可约'+item.availableCount+'位' */ '可以约': newData.unDisableText)}}</text>
							</view>
						</view>
					</block>
				</view>
				
				<!-- 确认按钮 -->
				<view class="bottom">
					<view class="show-time" v-if="!newData.isMultiple && !newData.isSection">
						{{orderDateTime?'预约时间：'+orderDateTime.slice(0):'暂无选择'}}
					</view>
					<view class="bottom_btn">
						<button form-type="submit" v-if="appointInfo.appoint" type="default" size="mini" class="cancle-btn" @click="giveUpResubmit">
							放弃重选
						</button>
						<button
							form-type="submit"
							type="default"
							size="mini"
							class="submit-btn"
							:class="{ 'submit-btn-loading': isSubmitting }"
							:disabled="isSubmitting"
							@click="handleSubmit">
							<text v-if="isSubmitting" class="loading-icon">⏳</text>
							<text>{{isSubmitting ? '提交中...' : (appointInfo.appoint?'确认重选':'确认预约')}}</text>
						</button>
					</view>
				</view>
			</view>
			<!-- 弹出日历 -->
			<view>
				<uni-calendar ref="calendar" :date="selectDate" :start-date="getstartDate()"
					:end-date="newData.dateRange[1]" :insert="false" @confirm="confirm" />
			</view>
		</view>
		<view v-if="resultVisible" style="padding-top:20rpx; background-color: white;">
			<view class="complete_box ">
				<view class="complete_font margin-bottom-sm flex align-center justify-center">
					您选择的档期
				</view>
				<view class="flex align-center justify-center " >
					<view class="complete_date_box_content">
						<view class="complete_font_date  flex align-center justify-center">
							{{timeStamp(appointInfo.appoint.startTime.split(" ")[0]).day}}
						</view>
						<view class="complete_font_date margin-bottom-sm flex align-center justify-center">
							{{appointInfo.appoint.startTime.split(" ")[0]}}
						</view>
					</view>
				</view>
				<view class="flex align-center padding-top-xs justify-center">
					<view class="complete_box_time flex align-center justify-center">
						{{appointInfo.appoint.startTime.split(" ")[1]}}
						<text class="complete_box_icon cuIcon-check"></text>
					</view>
				</view>
			</view>
			<view class="re_bottom" v-if="changeBtnVisible">
				<button form-type="submit" type="default" size="mini" class="re-submit-btn" @click="resubmit">
					重新选档
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	import api from '@/utils/api';
	export default {
		name: 'times',
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			appointTime: { // 预约的时间
				type: Array,
				default () {
					return []
				}
			},
		},
		watch: {
			appointTime: {
				handler(val) {
					if (val && val.length) {
						// 监听时间改变逻辑
					}
				}
			},
		},
		data() {
			return {
				newData: this.value,
				changeBtnVisible: false, // 重选按钮
				calendarVisible: false, // 档期选择
				resultVisible: false, // 档期结果选择
				appointInfo: '', // 档期规则+选档人信息
				orderDateTime: null, // 选中时间
				orderTimeArr: {}, // 多选的时间
				dateArr: [], // 日期数据
				timeArr: [], // 时间数据
				nowDate: "", // 当前日期
				dateActive: 0, // 选中的日期索引
				timeActive: null, // 选中的时间索引
				timeQuanBeginIndex: 0, // 时间段开始的下标
				selectDate: "", // 选择的日期
				selectWeek: "", // 选择的星期
				selectTime: "", // 选择的时间
				timeQuanBegin: "", // 时间段开始时间
				timeQuanEnd: "", // 时间段结束时间
				shopArray: [], // 店铺数组
				shopIndex: 0,
				// 小程序专用数据
				pageOptions: {}, // 小程序页面参数
				isMP: false, // 是否为小程序环境
				// 按钮状态控制
				isSubmitting: false, // 提交中状态
				submitTimer: null, // 防抖定时器
			}
		},
		created() {
			console.log('组件创建，接收到的props:', {
				value: this.value,
				appointTime: this.appointTime
			});

			// 直接初始化，不使用条件编译
			console.log('开始初始化组件');
			this.initOnload();
		},
		// 小程序生命周期方法
		onLoad(options) {
			// 小程序环境下的初始化
			// #ifdef MP-WEIXIN
			this.pageOptions = options || {};
			this.initOnload();
			// #endif
		},

		onShow() {
			// 小程序页面显示时的处理
			// #ifdef MP-WEIXIN
			// 可以在这里处理页面显示时的逻辑
			// #endif
		},

		// 组件销毁时清理定时器
		beforeDestroy() {
			if (this.submitTimer) {
				clearTimeout(this.submitTimer);
				this.submitTimer = null;
			}
		},
		methods: {
			// 初始化加载
			initOnload() {
				// 初始化日期
				this.initDates();
				// 获取预约信息
				this.getAppointInfo();
			},
			
			// 初始化日期相关数据
			initDates() {
				console.log('初始化日期数据，newData:', this.newData);

				this.nowDate = this.formatDate(new Date());

				// 检查dateRange是否存在
				if (!this.newData || !this.newData.dateRange || !this.newData.dateRange[0]) {
					console.error('dateRange数据缺失:', this.newData);
					// 设置默认日期范围（当前日期开始的30天）
					const today = this.formatDate(new Date());
					const endDate = this.formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000));

					if (!this.newData) {
						this.newData = {};
					}
					this.newData.dateRange = [today, endDate];

					this.showToast('日期配置缺失，使用默认配置');
				}

				this.selectDate = this.newData.dateRange[0];
				this.selectWeek = this.getWeekDay(this.newData.dateRange[0]);

				console.log('初始化完成 - nowDate:', this.nowDate, 'selectDate:', this.selectDate);
			},
			
			// 获取预约信息
			getAppointInfo() {
				let params = {};

				// #ifdef H5
				params = {
					pageId: util.getUrlParam(location.href, "page_id")
				};
				console.log('H5环境获取参数:', params);
				// #endif

				// #ifdef MP-WEIXIN
				// 优先使用存储的页面参数，兼容性更好
				if (this.pageOptions && this.pageOptions.page_id) {
					params = {
						pageId: this.pageOptions.page_id
					};
				} else {
					// 备用方案：从当前页面获取参数
					try {
						const pages = getCurrentPages();
						const currentPage = pages[pages.length - 1];
						const options = currentPage.options || {};
						params = {
							pageId: options.page_id || ''
						};
					} catch (error) {
						console.warn('获取小程序页面参数失败:', error);
						params = { pageId: '' };
					}
				}
				console.log('小程序环境获取参数:', params);
				// #endif

				console.log('调用getAppointInfo，参数:', params);

				api.getAppointInfo(params).then(res => {
					console.log('getAppointInfo响应:', res);

					if (!res || !res.data) {
						console.error('API响应数据为空');
						this.showToast('获取预约信息失败，数据为空');
						return;
					}

					this.appointInfo = res.data;
					this.shopArray = res.data.shopList;

					// 根据是否已预约显示不同界面
					if (this.appointInfo.appoint) {
						this.resultVisible = true;
					} else {
						this.calendarVisible = true;
					}

					// 是否显示重选按钮
					this.changeBtnVisible = this.appointInfo.changeFlag;

					// 初始化日期和时间数组
					this.dateArr = this.initDate();
					this.timeArr = this.initTime();

					// 加载档期数据
					this.getScheduleDate();
				}).catch(err => {
					console.error("获取预约信息失败", err);
					this.showToast('获取预约信息失败，请检查网络连接');
					// 显示默认界面，避免白屏
					this.calendarVisible = true;
				});
			},
			
			// 格式化日期为 YYYY-MM-DD
			formatDate(date) {
				const y = date.getFullYear();
				const m = date.getMonth() + 1;
				const d = date.getDate();
				return y + '-' + this.strFormat(m) + '-' + this.strFormat(d);
			},
			
			// 获取星期几
			getWeekDay(dateStr) {
				const days = ['日', '一', '二', '三', '四', '五', '六'];
				const date = new Date(dateStr.replace(/-/g, "/"));
				return `星期${days[date.getDay()]}`;
			},
			
			// 数字格式化，小于10前面加0
			strFormat(str) {
				return str < 10 ? `0${str}` : str;
			},
			
			// 获取时间戳和格式化信息
			timeStamp(time) {
				// 确保在小程序环境中正确处理日期
				let dates;
				
				// #ifdef MP-WEIXIN
				// 小程序环境下特殊处理日期
				if (typeof time === 'string') {
					// 如果是字符串，尝试替换'-'为'/'以兼容iOS
					dates = new Date(time.replace ? time.replace(/-/g, "/") : time);
				} else {
					// 如果是时间戳，直接创建Date对象
					dates = new Date(time);
				}
				// #endif
				
				// #ifndef MP-WEIXIN
				// 非小程序环境
				dates = new Date(time.replace ? time.replace(/-/g, "/") : time);
				// #endif
				
				const year = dates.getFullYear();
				const month = dates.getMonth() + 1;
				const date = dates.getDate();
				const day = dates.getDay();
				const hour = dates.getHours();
				const min = dates.getMinutes();
				const days = ['日', '一', '二', '三', '四', '五', '六'];
				return {
					allDate: `${year}/${this.strFormat(month)}/${this.strFormat(date)}`,
					date: `${this.strFormat(year)}-${this.strFormat(month)}-${this.strFormat(date)}`,
					day: `星期${days[day]}`,
					hour: this.strFormat(hour) + ':' + this.strFormat(min) + ':00'
				}
			},

			// 获取当前时间
			currentTime() {
				const myDate = new Date();
				const y = myDate.getFullYear();
				const m = myDate.getMonth() + 1;
				const d = myDate.getDate();
				const date = y + '-' + this.strFormat(m) + '-' + this.strFormat(d);

				const hour = myDate.getHours();
				const min = myDate.getMinutes();
				const secon = myDate.getSeconds();
				const time = this.strFormat(hour) + ':' + this.strFormat(min) + ':' + this.strFormat(secon);
				return {
					date,
					time
				}
			},
			
			// 获取最近7天的日期和礼拜天数
			initDate() {
				console.log('初始化日期数组，当前数据:', {
					nowDate: this.nowDate,
					dateRange: this.newData?.dateRange
				});

				// 检查必要数据，如果不完整则使用默认值
				if (!this.newData || !this.newData.dateRange || !this.newData.dateRange[0] || !this.newData.dateRange[1]) {
					console.warn('initDate: dateRange数据不完整，使用默认配置');
					// 使用默认日期范围（从今天开始的30天）
					const today = this.formatDate(new Date());
					const endDate = this.formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000));

					if (!this.newData) {
						this.newData = {};
					}
					this.newData.dateRange = [today, endDate];
				}

				let dateList = [];
				const oneDay = 3600 * 24 * 1000; // 一天的毫秒数

				// 确定开始日期
				let beginDate;
				const nowTimestamp = new Date(this.nowDate.replace(/-/g, "/")).getTime();
				const rangeStartTimestamp = new Date(this.newData.dateRange[0].replace(/-/g, "/")).getTime();

				if (nowTimestamp < rangeStartTimestamp) {
					beginDate = this.newData.dateRange[0];
				} else {
					this.selectDate = this.nowDate;
					beginDate = this.nowDate;
				}

				const endDate = this.newData.dateRange[1].replace(/-/g, "/");

				// 计算显示的天数，最多显示7天
				let dateLength = Math.min(7, Math.floor(
					(new Date(endDate).getTime() - new Date(beginDate.replace(/-/g, "/")).getTime()) / oneDay
				)) + 1;

				// 确保至少显示1天
				if (dateLength < 1) {
					dateLength = 1;
				}

				// 生成日期列表
				const beginTimestamp = new Date(beginDate.replace(/-/g, "/")).getTime();
				for (let i = 0; i < dateLength; i++) {
					const currentTimestamp = beginTimestamp + oneDay * i;
					const nowTimestamp = new Date(this.nowDate.replace(/-/g, "/")).getTime();
					dateList.push({
						date: this.timeStamp(currentTimestamp).date,
						timeStamp: currentTimestamp,
						week: currentTimestamp === nowTimestamp ? "今天" :
							(currentTimestamp === nowTimestamp + oneDay ? "明天" :
							(currentTimestamp === nowTimestamp + oneDay * 2 ? "后天" :
							this.timeStamp(currentTimestamp).day))
					});
				}

				console.log('生成的日期列表:', dateList);
				return dateList;
			},
			
			// 初始化时间数组
			initTime() {
				let timeList = [];

				// 检查必要数据，如果不完整则使用默认值
				if (!this.newData) {
					this.newData = {};
				}

				// 设置默认值
				const hourInterval = this.newData.hourInterval || 0;
				const minuteInterval = this.newData.minuteInterval || 30;
				const timeRangeAm = this.newData.timeRangeAm || ['09:00:00', '12:00:00'];
				const timeRangePm = this.newData.timeRangePm || ['14:00:00', '18:00:00'];

				// 计算时间间隔（分钟）
				const timeInterval = (3600 * 1000 * hourInterval + 60 * 1000 * minuteInterval);

				// 处理上午时间段
				this.addTimeSlots(timeList, timeRangeAm[0], timeRangeAm[1], timeInterval);

				// 处理下午时间段
				this.addTimeSlots(timeList, timeRangePm[0], timeRangePm[1], timeInterval);

				console.log('生成的时间列表:', timeList);
				return timeList;
			},
			
			// 添加时间段
			addTimeSlots(timeList, startTime, endTime, interval) {
				// 确保有选择的日期
				if (!this.selectDate) {
					this.selectDate = this.nowDate || this.formatDate(new Date());
				}

				// 确保时间间隔有效
				if (!interval || interval <= 0) {
					interval = 30 * 60 * 1000; // 默认30分钟间隔
				}

				try {
					const startTimestamp = new Date((this.selectDate + " " + startTime).replace(/-/g, "/")).getTime();
					const endTimestamp = new Date((this.selectDate + " " + endTime).replace(/-/g, "/")).getTime();

					// 检查时间戳是否有效
					if (isNaN(startTimestamp) || isNaN(endTimestamp)) {
						console.error('时间格式错误:', startTime, endTime);
						return;
					}

					for (let i = startTimestamp; i <= endTimestamp; i += interval) {
						if(i + interval > endTimestamp){
							break;
						}
						timeList.push({
							time: this.timeStamp(i).hour,
							disable: false,
							availableCount: 0
						});
					}
				} catch (error) {
					console.error('添加时间段失败:', error, startTime, endTime);
				}
			},
			
			// 日期选择事件
			selectDateEvent(index, item) {
				if (this.dateActive === index) {
					return;
				}
				
				this.dateActive = index;
				this.selectDate = item.date;
				this.selectWeek = item.week;
				this.timeActive = null;
				this.orderDateTime = null;
				
				// 更新时间列表
				this.timeArr = this.initTime();
				
				// 加载选定日期的档期数据
				this.getScheduleDate();
			},

			// 时间选择事件
			selectTimeEvent(index, item) {
				// 如果时间段被禁用，不做任何操作
				if (item.disable) return;
				
				if (this.newData.isMultiple) {
					// 多选模式
					item.isActive = !item.isActive;
					this.timeArr = [...this.timeArr]; // 创建新数组触发视图更新
					
					// 更新已选时间数组
					this.orderTimeArr[this.selectDate] = this.timeArr
						.filter(time => time.isActive)
						.map(time => time.time);
				} else {
					// 单选模式
					this.timeActive = index;
					this.selectTime = item.time;
					this.orderDateTime = `${this.selectDate} ${item.time}`;
				}
			},

			// 选择时间段
			handleSelectQuantum(index, item) {
				// 如果时间段被禁用，不做任何操作
				if (item.disable) return;
				
				// 如果没有选择开始时间，则设置开始时间
				if (!this.timeQuanBegin) {
					this.timeQuanBeginIndex = index;
					this.timeQuanBegin = item.time;
					this.timeQuanEnd = "";
					return;
				}
				
				// 如果已选择开始时间但未选择结束时间
				if (this.timeQuanBegin && !this.timeQuanEnd) {
					// 确定开始和结束的索引范围
					let startIndex = this.timeQuanBeginIndex;
					let endIndex = index;
					
					// 确保开始索引小于结束索引
					if (startIndex > endIndex) {
						[startIndex, endIndex] = [endIndex, startIndex];
					}
					
					// 检查中间是否有禁用的时间段
					let hasDisabledSlot = false;
					for (let i = startIndex + 1; i < endIndex; i++) {
						if (this.timeArr[i].disable) {
							hasDisabledSlot = true;
							break;
						}
					}
					
					// 如果中间有禁用时间段，重新设置开始时间
					if (hasDisabledSlot) {
						this.timeQuanBeginIndex = index;
						this.timeQuanBegin = item.time;
						this.timeQuanEnd = "";
						return;
					}
					
					// 标记中间的时间段为包含状态
					for (let i = startIndex + 1; i < endIndex; i++) {
						this.timeArr[i].isInclude = true;
					}
					
					// 设置结束时间
					this.timeQuanEnd = item.time;
					return;
				}
				
				// 如果已经选择了完整的时间段，则重新开始选择
				if (this.timeQuanBegin && this.timeQuanEnd) {
					// 清除所有包含状态
					this.timeArr.forEach(t => {
						t.isInclude = false;
					});
					
					// 重新设置开始时间
					this.timeQuanBeginIndex = index;
					this.timeQuanBegin = item.time;
					this.timeQuanEnd = "";
				}
			},
			
			// 日期导航（前进/后退）
			changDate(type) {
				const oneDay = 3600 * 24 * 1000; // 一天的毫秒数
				const dateLength = 7; // 固定显示7天
				let newDateArr = [];
				
				if (type === 'back') {
					// 向前导航
					this.navigateBackward(newDateArr, dateLength, oneDay);
				} else if (type === 'next') {
					// 向后导航
					this.navigateForward(newDateArr, dateLength, oneDay);
				}
				
				// 更新日期数组
				this.dateArr = newDateArr;
				this.dateActive = 0;
				this.selectDate = this.dateArr[0].date;
				this.selectWeek = this.dateArr[0].week;
				
				// 更新时间列表
				this.timeArr = this.initTime();
				
				// 加载新日期的档期数据
				this.getScheduleDate();
			},
			
			// 向前导航
			navigateBackward(dateArr, dateLength, oneDay) {
				const minTimestamp = new Date(this.newData.dateRange[0].replace(/-/g, "/")).getTime();
				const nowTimestamp = new Date(this.nowDate.replace(/-/g, "/")).getTime();
				const currentFirstDay = this.dateArr[0].timeStamp;
				
				// 计算新的开始日期
				let newStartTimestamp;
				
				// 如果向前移动会小于当前日期或最小日期，则使用较大的那个作为起始日期
				if (currentFirstDay - oneDay * dateLength <= Math.max(nowTimestamp, minTimestamp)) {
					newStartTimestamp = Math.max(nowTimestamp, minTimestamp);
				} else {
					// 正常情况，向前移动7天
					newStartTimestamp = currentFirstDay - oneDay * dateLength;
				}
				
				// 生成新的日期数组
				for (let i = 0; i < dateLength; i++) {
					const timestamp = newStartTimestamp + oneDay * i;
					dateArr.push({
						date: this.timeStamp(timestamp).date,
						timeStamp: timestamp,
						week: i === 0 && timestamp === nowTimestamp ? "今天" : 
							(i === 1 && timestamp === nowTimestamp + oneDay ? "明天" : 
							(i === 2 && timestamp === nowTimestamp + oneDay * 2 ? "后天" : 
							this.timeStamp(timestamp).day))
					});
				}
			},
			
			// 向后导航
			navigateForward(dateArr, dateLength, oneDay) {
				const maxTimestamp = new Date(this.newData.dateRange[1].replace(/-/g, "/")).getTime();
				const currentLastDay = this.dateArr[this.dateArr.length - 1].timeStamp;
				const nowTimestamp = new Date(this.nowDate.replace(/-/g, "/")).getTime();
				
				// 计算新的开始日期
				let newStartTimestamp;
				
				// 如果向后移动会超出最大日期范围，则从最大日期倒推7天
				if (currentLastDay + oneDay >= maxTimestamp) {
					newStartTimestamp = maxTimestamp - oneDay * (dateLength - 1);
				} else {
					// 正常情况，向后移动7天
					newStartTimestamp = this.dateArr[0].timeStamp + oneDay * dateLength;
				}
				
				// 生成新的日期数组
				for (let i = 0; i < dateLength; i++) {
					const timestamp = newStartTimestamp + oneDay * i;
					dateArr.push({
						date: this.timeStamp(timestamp).date,
						timeStamp: timestamp,
						week: timestamp === nowTimestamp ? "今天" : 
							(timestamp === nowTimestamp + oneDay ? "明天" : 
							(timestamp === nowTimestamp + oneDay * 2 ? "后天" : 
							this.timeStamp(timestamp).day))
					});
				}
			},
			
			// 获取档期数据
			getScheduleDate() {
				// 确保有店铺被选中
				if (!this.shopArray || !this.shopArray[this.shopIndex]) {
					return;
				}
				
				const params = {
					startTime: this.selectDate + " 00:00:00",
					roomId: this.shopArray[this.shopIndex].roomIdList[0]
				};
				
				api.getScheduleDate(params).then(res => {
					const appointTime = res.data;
					
					// 更新时间段状态
					this.updateTimeSlotStatus(appointTime);
					
					// 检查禁用规则
					this.checkDisable();
				}).catch(err => {
					console.error("获取档期数据失败", err);
				});
			},
			
			// 更新时间段状态
			updateTimeSlotStatus(appointTime) {
				this.timeArr.forEach(item => {
					//判断是当前这一天，选中时间小于当前时间则禁用
					if (this.selectDate == this.nowDate && this.currentTime().time > item.time) {
						item.disable = true
					} else {
						item.disable = false
					}
					
					// 处理最大可预约次数逻辑
					let currentTimeSlot = item.time.substring(0, 5) + "-" + this.getNextTimeSlot(item.time);

					let maxCount = (this.newData && this.newData.maxSelectCount) ? this.newData.maxSelectCount : 1; // 默认最大预约次数为1
					let usedCount = 0;

					// 检查scheduledData中是否有特定日期和时间段的设置
					if (this.newData && this.newData.scheduledData && this.newData.scheduledData[this.selectDate] &&
						this.newData.scheduledData[this.selectDate][currentTimeSlot] !== undefined) {
						maxCount = this.newData.scheduledData[this.selectDate][currentTimeSlot];
					}
					
					// 计算已使用的预约次数
					if (appointTime) {
						appointTime.forEach(t => {
							let appointStartTime = t.startTime.split(" ")[1];
							if (appointStartTime === item.time) {
								usedCount++;
							}
						});
					}
					// 计算可用预约次数
					item.availableCount = Math.max(0, maxCount - usedCount);
					
					// 如果已预约次数达到或超过最大次数，则禁用
					if (usedCount >= maxCount) {
						item.disable = true;
					}
					
					// 将预约的时间禁用  时间段禁用 - 移除这部分逻辑，改为在上面的判断中处理
					let nowTimeStamp = new Date((this.selectDate + " " + item.time).replace(/-/g, "/")).getTime();
					// 这部分逻辑不再需要，因为我们已经在上面基于maxCount和usedCount做了判断
					// if (appointTime) {
					// 	appointTime.forEach(t => {
					// 		let startTimeStamp = new Date(t.startTime.replace(/-/g, "/")).getTime();
					// 		let endTimeStamp = new Date(t.endTime.replace(/-/g, "/")).getTime();
					// 		if (startTimeStamp <= nowTimeStamp && nowTimeStamp <
					// 			endTimeStamp) {
					// 			item.disable = true
					// 		}
					// 	})
					// }
					// 禁用时间段
					// const cur_time = `${this.selectDate} ${item.time}`
					// const {
					// 	begin_time,
					// 	end_time
					// } = this.newData.disableTimeSlot
					// if (begin_time && end_time && (begin_time <= cur_time && cur_time <= end_time)) {
					// 	item.disable = true
					// }

					// 判断是否当前日期时间都被预约
					// if (!item.disable) {
					// 	isFullTime = false
					// }
					this.newData && this.newData.isSection && (item.isInclude = false)
				})
				//禁用时间段
				this.checkDisable();

			},
			
			// 检查禁用时间段
			checkDisable() {
				// 检查日期是否在禁用日期列表中
				const isDateDisabled = this.isDateDisabled();
				// 检查是否是禁用的周末
				const isWeekendDisabled = this.isWeekendDisabled();
				
				// 如果整个日期被禁用，禁用所有时间段
				if (isDateDisabled || isWeekendDisabled) {
					this.timeArr.forEach(item => {
						item.disable = true;
					});
					return;
				}
				
				// 检查单个时间段是否在禁用时间段内
				this.checkDisabledTimeSlots();
			},
			
			// 检查日期是否被禁用
			isDateDisabled() {
				// 检查必要数据
				if (!this.newData || !this.newData.unableDateList || !Array.isArray(this.newData.unableDateList)) {
					return false;
				}

				const currentDate = new Date(this.selectDate.replace(/-/g, "/")).getTime();

				for (const disableItem of this.newData.unableDateList) {
					// 检查日期段类型
					if (disableItem.type === 2 && disableItem.date && Array.isArray(disableItem.date) && disableItem.date.length >= 2) {
						const startDate = new Date(disableItem.date[0].replace(/-/g, "/")).getTime();
						const endDate = new Date(disableItem.date[1].replace(/-/g, "/")).getTime();

						if (currentDate >= startDate && currentDate <= endDate) {
							return true;
						}
					}
				}

				return false;
			},
			
			// 检查是否是禁用的周末
			isWeekendDisabled() {
				// 检查必要数据
				if (!this.newData || !this.newData.unableDateList || !Array.isArray(this.newData.unableDateList)) {
					return false;
				}

				const isWeekend = this.selectWeek === "星期六" || this.selectWeek === "星期日";

				if (!isWeekend) {
					return false;
				}

				// 检查是否有禁用周末的规则
				for (const disableItem of this.newData.unableDateList) {
					if (disableItem.type === 1 && disableItem.rule === 2) {
						return true;
					}
				}

				return false;
			},
			
			// 检查禁用的时间段
			checkDisabledTimeSlots() {
				// 检查必要数据
				if (!this.newData || !this.newData.unableTimeList || !Array.isArray(this.newData.unableTimeList)) {
					return;
				}

				this.timeArr.forEach(item => {
					if (item.disable) return; // 如果已经被禁用，跳过检查

					const currentTime = new Date((this.selectDate + " " + item.time).replace(/-/g, "/")).getTime();

					// 检查是否在禁用时间段内
					for (const timeRange of this.newData.unableTimeList) {
						if (!Array.isArray(timeRange) || timeRange.length < 2) {
							continue;
						}

						const startTime = new Date((this.selectDate + " " + timeRange[0]).replace(/-/g, "/")).getTime();
						const endTime = new Date((this.selectDate + " " + timeRange[1]).replace(/-/g, "/")).getTime();

						if (currentTime >= startTime && currentTime <= endTime) {
							item.disable = true;
							break;
						}
					}
				});
			},
			
			// 处理提交预约
			handleSubmit() {
				// 防抖处理：如果正在提交中，直接返回
				if (this.isSubmitting) {
					return;
				}

				// 清除之前的定时器
				if (this.submitTimer) {
					clearTimeout(this.submitTimer);
				}

				// 设置防抖定时器，300ms内重复点击无效
				this.submitTimer = setTimeout(() => {
					this.doSubmit();
				}, 300);
			},

			// 实际执行提交逻辑
			doSubmit() {
				// 验证是否可以提交
				if (!this.validateSubmit()) {
					return;
				}

				// 设置提交中状态
				this.isSubmitting = true;

				// 根据模式处理不同的提交逻辑
				if (this.newData.isSection) {
					this.submitSectionAppointment();
				} else if (this.newData.isMultiple) {
					this.submitMultipleAppointment();
				} else {
					this.submitSingleAppointment();
				}
			},
			
			// 验证提交条件
			validateSubmit() {
				if (!this.shopArray) {
					this.showModal('提示', '没有配置店铺', false);
					return false;
				}

				if (!this.appointInfo.addFlag) {
					this.showToast('不满足选档条件');
					return false;
				}

				if (!this.orderDateTime && !this.newData.isMultiple && !this.newData.isSection) {
					this.showToast('请选择具体档期');
					return false;
				}

				if (this.newData.isSection && (!this.timeQuanBegin || !this.timeQuanEnd)) {
					this.showToast('请选择完整的时间段');
					return false;
				}

				return true;
			},
			
			// 提交单个时间段预约
			submitSingleAppointment() {
				// 计算结束时间
				const endTimeStamp = new Date(this.orderDateTime.replace(/-/g, "/")).getTime() + 
					(3600 * 1000 * (this.newData.timeInterval || 0) + 60 * 1000 * this.newData.minuteInterval);
				const endTime = this.timeStamp(endTimeStamp).date + " " + this.timeStamp(endTimeStamp).hour;
				
				const params = {
					startTime: this.orderDateTime,
					endTime: endTime,
					shopId: this.shopArray[this.shopIndex].id,
					roomId: this.shopArray[this.shopIndex].roomIdList[0],
				};
				
				// 获取页面ID
				// #ifdef H5
				params.pageId = util.getUrlParam(location.href, "page_id");
				// #endif

				// #ifdef MP-WEIXIN
				// 优先使用存储的页面参数
				if (this.pageOptions && this.pageOptions.page_id) {
					params.pageId = this.pageOptions.page_id;
				} else {
					// 备用方案
					try {
						const pages = getCurrentPages();
						const currentPage = pages[pages.length - 1];
						const options = currentPage.options || {};
						params.pageId = options.page_id || '';
					} catch (error) {
						console.warn('获取小程序页面参数失败:', error);
						params.pageId = '';
					}
				}
				// #endif
				
				// 根据是否已有预约决定调用不同的API
				if (!this.appointInfo.appoint) {
					// 新增预约
					api.addAppoint(params).then(() => {
						this.calendarVisible = false;
						this.initOnload();
					}).catch(err => {
						console.error("提交预约失败", err);
						this.showToast('提交失败，请重试');
					}).finally(() => {
						// 重置提交状态
						this.isSubmitting = false;
					});
				} else {
					// 修改预约
					params.appointId = this.appointInfo.appoint.id;
					api.resetAppoint(params).then(() => {
						this.showModal('提示', '恭喜您修改成功！请注意约档时间', false);
						this.calendarVisible = false;
						this.initOnload();
					}).catch(err => {
						console.error("修改预约失败", err);
						this.showToast('修改失败，请重试');
					}).finally(() => {
						// 重置提交状态
						this.isSubmitting = false;
					});
				}
			},
			
			// 提交时间段预约
			submitSectionAppointment() {
				try {
					// 确保开始时间小于结束时间
					this.handleChange();
					this.$emit('change', {
						beginTime: `${this.selectDate} ${this.timeQuanBegin}`,
						endTime: `${this.selectDate} ${this.timeQuanEnd}`
					});
				} finally {
					// 重置提交状态
					this.isSubmitting = false;
				}
			},

			// 提交多选时间预约
			submitMultipleAppointment() {
				try {
					let time = [];
					for (let date in this.orderTimeArr) {
						this.orderTimeArr[date].forEach(item => {
							time.push(`${date} ${item}`);
						});
					}
					this.$emit('change', time);
				} finally {
					// 重置提交状态
					this.isSubmitting = false;
				}
			},
			
			// 确保时间段的开始时间小于结束时间
			handleChange() {
				if (this.timeQuanBegin > this.timeQuanEnd) {
					[this.timeQuanBegin, this.timeQuanEnd] = [this.timeQuanEnd, this.timeQuanBegin];
				}
			},
			
			// 店铺选择变化
			bindPickerChange(e) {
				this.shopIndex = e.target.value;
				// 店铺变化时重新加载档期数据
				this.getScheduleDate();
			},
			
			// 打开日历
			openCalendar() {
				this.$refs.calendar.open();
			},
			
			// 日历确认选择
			confirm(e) {
				this.selectDate = e.fulldate;
				this.updateDateArray();
				
				// 更新时间列表
				this.timeArr = this.initTime();
				
				// 加载新选择日期的档期数据
				this.getScheduleDate();
			},
			
			// 更新日期数组
			updateDateArray() {
				const oneDay = 3600 * 24 * 1000; // 一天的毫秒数
				const selectedTimestamp = new Date(this.selectDate.replace(/-/g, "/")).getTime();
				const maxEndTimestamp = new Date(this.newData.dateRange[1].replace(/-/g, "/")).getTime();
				
				let dateArr = [];
				let dateLength = 7;
				
				// 确保不超出最大日期范围
				if (selectedTimestamp + oneDay * 6 > maxEndTimestamp) {
					// 如果选择的日期加6天超出范围，则从最大日期倒推7天
					for (let i = 0; i < dateLength; i++) {
						const timestamp = maxEndTimestamp - oneDay * (dateLength - 1 - i);
						dateArr.push({
							date: this.timeStamp(timestamp).date,
							timeStamp: timestamp,
							week: this.timeStamp(timestamp).day
						});
					}
				} else {
					// 正常情况，从选择的日期开始显示7天
					for (let i = 0; i < dateLength; i++) {
						const timestamp = selectedTimestamp + oneDay * i;
						dateArr.push({
							date: this.timeStamp(timestamp).date,
							timeStamp: timestamp,
							week: i === 0 ? "今天" : (i === 1 ? "明天" : (i === 2 ? "后天" : this.timeStamp(timestamp).day))
						});
					}
				}
				
				this.dateArr = dateArr;
				
				// 找到选中日期在数组中的索引
				for (let i = 0; i < this.dateArr.length; i++) {
					if (this.dateArr[i].date === this.selectDate) {
						this.dateActive = i;
						this.selectWeek = this.dateArr[i].week;
						break;
					}
				}
			},
			
			// 重新选档
			resubmit() {
				this.resultVisible = false;
				this.calendarVisible = true;
			},
			
			// 放弃重选
			giveUpResubmit() {
				this.resultVisible = true;
				this.calendarVisible = false;
			},
			
			// 检查是否显示前进/后退按钮
			checkIcon(type) {
				const oneDay = 3600 * 24 * 1000; // 一天的毫秒数
				const dateLength = Math.floor((new Date(this.newData.dateRange[1].replace(/-/g, "/")).getTime() - 
					new Date(this.newData.dateRange[0].replace(/-/g, "/")).getTime()) / oneDay);
				
				// 如果总日期范围小于7天，不显示导航按钮
				if (dateLength < 7) {
					return false;
				}
				
				if (type === 'back') {
					// 不可小于当前时间和最小值
					const nowTimestamp = new Date(this.nowDate.replace(/-/g, "/")).getTime();
					const minTimestamp = new Date(this.newData.dateRange[0].replace(/-/g, "/")).getTime();
					
					return this.dateArr[0].timeStamp > nowTimestamp && this.dateArr[0].timeStamp > minTimestamp;
				} else if (type === 'next') {
					// 不可大于最大日期
					const maxTimestamp = new Date(this.newData.dateRange[1].replace(/-/g, "/")).getTime();
					return this.dateArr[this.dateArr.length - 1].timeStamp < maxTimestamp;
				}
				
				return false;
			},
			
			// 检查是否显示更多时间选择按钮
			checkDateSelect() {
				console.log(123);
				
				const oneDay = 3600 * 24 * 1000; // 一天的毫秒数
				const totalDays = Math.floor((new Date(this.newData.dateRange[1].replace(/-/g, "/")).getTime() - 
					new Date(this.newData.dateRange[0].replace(/-/g, "/")).getTime()) / oneDay);
					console.log(totalDays);
				
				// 如果总日期范围大于7天，显示更多时间选择按钮
				return totalDays > 7;
			},
			
			// 获取开始日期
			getstartDate() {
				// 确保数据存在
				if (!this.newData || !this.newData.dateRange || !this.newData.dateRange[0]) {
					return this.nowDate || this.formatDate(new Date());
				}

				const nowTimestamp = new Date(this.nowDate.replace(/-/g, "/")).getTime();
				const minTimestamp = new Date(this.newData.dateRange[0].replace(/-/g, "/")).getTime();

				if (nowTimestamp < minTimestamp) {
					return this.newData.dateRange[0];
				} else {
					return this.nowDate;
				}
			},
			
			// 计算下一个时间段
			getNextTimeSlot(time) {
				let [hours, minutes] = time.split(':');
				let totalMinutes = parseInt(hours) * 60 + parseInt(minutes);
				totalMinutes += this.newData.minuteInterval;

				let newHours = Math.floor(totalMinutes / 60);
				let newMinutes = totalMinutes % 60;

				return this.strFormat(newHours) + ':' + this.strFormat(newMinutes);
			},

			// 小程序专用方法：显示提示信息
			showToast(title, icon = 'none', duration = 2000) {
				// #ifdef MP-WEIXIN
				uni.showToast({
					title: title,
					icon: icon,
					duration: duration
				});
				// #endif

				// #ifndef MP-WEIXIN
				uni.showToast({
					title: title,
					icon: icon,
					duration: duration
				});
				// #endif
			},

			// 小程序专用方法：显示模态框
			showModal(title, content, showCancel = true) {
				return new Promise((resolve, reject) => {
					// #ifdef MP-WEIXIN
					uni.showModal({
						title: title,
						content: content,
						showCancel: showCancel,
						success: (res) => {
							resolve(res);
						},
						fail: (err) => {
							reject(err);
						}
					});
					// #endif

					// #ifndef MP-WEIXIN
					uni.showModal({
						title: title,
						content: content,
						showCancel: showCancel,
						success: (res) => {
							resolve(res);
						},
						fail: (err) => {
							reject(err);
						}
					});
					// #endif
				});
			}

		}
	}
</script>
<style lang="scss" scoped>
	@import './index.scss';

	page {
		height: 100%;
	}

	.content {
		text-align: center;
		height: 100%;
	}

	/* 底部 - 固定在页面底部，支持安全区 */
	.bottom {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		flex-direction: row;
		width: 100%;
		background-color: #fff;
		z-index: 999;
		border-top: 1px solid #eee;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.show-time {
		width: 60%;
		color: #505050;
		background-color: rgba(255, 255, 255, 1);
		font-size: 30rpx;
		line-height: 94rpx;
		text-align: center;
	}
	
	.bottom_btn{
		width: 40%;
		display: flex;
		flex-direction: row;
		gap: 10rpx;
		margin-right: 20rpx;
	}

	.cancle-btn {
		width: 100%;
		height: 80rpx;
		color: white;
		background-color:  #E6A23C;
		font-size: 30rpx;
		line-height: 80rpx;
		text-align: center;
		margin: auto;
	}
	.submit-btn {
		width: 100%;
		height: 80rpx;
		color: white;
		background-color:  #27bb25;
		font-size: 30rpx;
		line-height: 80rpx;
		text-align: center;
		margin: auto;
		transition: all 0.3s ease;
	}

	/* 提交按钮加载状态样式 */
	.submit-btn-loading {
		background-color: #a0a0a0 !important;
		opacity: 0.7;
		cursor: not-allowed;
	}

	.loading-icon {
		margin-right: 10rpx;
		animation: rotate 1s linear infinite;
	}

	/* 旋转动画 */
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	.fontw {
		font-weight: bold;
	}

	.borderb {
		border-bottom: 2px solid #27bb25;
	}

	.complete_box {
		padding: 90rpx;
		border: 2rpx solid #86909c;
		margin: 0 80rpx 0 80rpx;
	}

	.complete_font {
		font-size: 50rpx;
	}
	.complete_date_box_content{
		color: #27bb25;
		width: 50%;
		border-bottom: 4rpx solid #27bb25;
	}
	.complete_font_date {
		font-size: 30rpx;
	}

	.complete_box_time {
		border-radius: 10rpx;
		width: 35%;
		background: #f1f3f6;
		flex-direction: column;
		height: 154rpx;
		margin: 20rpx 0;
		padding: 34rpx;
		border: 2rpx solid #EEEEEE;
		font-weight: bold;
	}

	.complete_box_icon {
		color: #27bb25;
		font-size: 44rpx;
	}

	/* 重新选择 - 保持原来位置 */
	.re_bottom {
		display: flex;
		flex-direction: row;
		padding: 20rpx 0;
		top: auto;
		left: 0rpx;
		width: 100%;
		background-color: #fff;
	}

	.re-submit-btn {
		width: 45%;
		height: 80rpx;
		color: white;
		background-color: #27bb25;
		font-size: 30rpx;
		line-height: 80rpx;
		text-align: center;
		margin: auto;
		padding: 0;
	}
</style>
